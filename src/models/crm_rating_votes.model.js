import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";
import { RATING_VOTES } from "../region/config.js";

const CrmRatingVotes = sequelize.define(
    "CrmRatingVotes",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        rating_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        product_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        votes: {
            type: DataTypes.ENUM(RATING_VOTES.LIKE, RATING_VOTES.DISLIKE),
            allowNull: false,
            defaultValue: RATING_VOTES.LIKE,
        },
        created_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        updated_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
            onUpdate: DataTypes.NOW,
        },
    },
    {
        tableName: "crm_rating_votes",
        timestamps: false,
    }
);

CrmRatingVotes.associate = (models) => {
    CrmRatingVotes.belongsTo(models.CrmRatings, {
        foreignKey: "rating_id",
        targetKey: "rating_id",
        as: "rating",
    });

    CrmRatingVotes.belongsTo(models.CrmCustomer, {
        foreignKey: "customer_id",
        targetKey: "id",
        as: "user",
    });

    CrmRatingVotes.belongsTo(models.CatalogProduct, {
        foreignKey: "product_id",
        targetKey: "productid",
        as: "product",
    });
};

export default CrmRatingVotes;
